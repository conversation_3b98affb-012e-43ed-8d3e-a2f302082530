<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.AppTaskApplicationMapper">
    
    <resultMap type="AppTaskApplication" id="AppTaskApplicationResult">
        <result property="applicationId"    column="application_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="applicantId"    column="applicant_id"    />
        <result property="applicantName"    column="applicant_name"    />
        <result property="applicantAvatar"    column="applicant_avatar"    />
        <result property="applicationReason"    column="application_reason"    />
        <result property="applicationStatus"    column="application_status"    />
        <result property="createTime"    column="create_time"    />
        <result property="confirmTime"    column="confirm_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap type="AppTaskApplication" id="AppTaskApplicationWithTaskResult" extends="AppTaskApplicationResult">
        <association property="task" javaType="AppTask">
            <result property="taskId" column="task_id" />
            <result property="taskTitle" column="task_title" />
            <result property="taskDesc" column="task_desc" />
            <result property="taskAmount" column="task_amount" />
            <result property="taskStatus" column="task_status" />
            <result property="publisherId" column="publisher_id" />
            <result property="publisherName" column="publisher_name" />
            <result property="taskAddress" column="task_address" />
            <result property="startTime" column="start_time" />
            <result property="endTime" column="end_time" />
            <association property="publisherUserProfile" javaType="AppUserProfile">
                <result property="creditScore" column="publisher_credit_score" />
                <result property="sex" column="publisher_sex" />
                <result property="realName" column="publisher_real_name" />
                <result property="phonenumber" column="publisher_phonenumber" />
            </association>
        </association>
    </resultMap>

    <resultMap type="AppTaskApplication" id="AppTaskApplicationWithApplicantInfoResult" extends="AppTaskApplicationResult">
        <association property="applicantUserProfile" javaType="AppUserProfile">
            <result property="creditScore" column="applicant_credit_score" />
            <result property="sex" column="applicant_sex" />
            <result property="realName" column="applicant_real_name" />
            <result property="phonenumber" column="applicant_phonenumber" />
        </association>
    </resultMap>

    <sql id="selectAppTaskApplicationVo">
        select application_id, task_id, applicant_id, applicant_name, applicant_avatar, application_reason, application_status, create_time, confirm_time, remark from app_task_application
    </sql>

    <select id="selectAppTaskApplicationList" parameterType="AppTaskApplication" resultMap="AppTaskApplicationResult">
        <include refid="selectAppTaskApplicationVo"/>
        <where>  
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="applicantId != null "> and applicant_id = #{applicantId}</if>
            <if test="applicantName != null  and applicantName != ''"> and applicant_name like concat('%', #{applicantName}, '%')</if>
            <if test="applicationStatus != null  and applicationStatus != ''"> and application_status = #{applicationStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectAppTaskApplicationByApplicationId" parameterType="Long" resultMap="AppTaskApplicationResult">
        <include refid="selectAppTaskApplicationVo"/>
        where application_id = #{applicationId}
    </select>

    <select id="selectAppTaskApplicationListByTaskId" parameterType="Long" resultMap="AppTaskApplicationResult">
        <include refid="selectAppTaskApplicationVo"/>
        where task_id = #{taskId}
        order by create_time desc
    </select>

    <select id="selectAppTaskApplicationListByApplicantId" parameterType="Long" resultMap="AppTaskApplicationWithTaskResult">
        select a.application_id, a.task_id, a.applicant_id, a.applicant_name, a.applicant_avatar, 
               a.application_reason, a.application_status, a.create_time, a.confirm_time, a.remark,
               t.task_title, t.task_desc, t.task_amount, t.task_status, t.publisher_id, t.publisher_name,
               t.task_address, t.start_time, t.end_time
        from app_task_application a
        left join app_task t on a.task_id = t.task_id
        where a.applicant_id = #{applicantId}
        order by a.create_time desc
    </select>

    <select id="selectAppTaskApplicationByTaskIdAndApplicantId" resultMap="AppTaskApplicationResult">
        <include refid="selectAppTaskApplicationVo"/>
        where task_id = #{taskId} and applicant_id = #{applicantId}
    </select>

    <select id="selectAppTaskApplicationListByApplicantIdWithPublisherInfo" parameterType="Long" resultMap="AppTaskApplicationWithTaskResult">
        select a.application_id, a.task_id, a.applicant_id, a.applicant_name, a.applicant_avatar,
               a.application_reason, a.application_status, a.create_time, a.confirm_time, a.remark,
               t.task_title, t.task_desc, t.task_amount, t.task_status, t.publisher_id, t.publisher_name,
               t.task_address, t.start_time, t.end_time,
               up.credit_score as publisher_credit_score,
               u.sex as publisher_sex,
               CASE
                   WHEN u.real_name IS NOT NULL AND u.real_name != '' THEN
                       CONCAT(LEFT(u.real_name, 1), REPEAT('*', CHAR_LENGTH(u.real_name) - 1))
                   ELSE ''
               END as publisher_real_name,
               CASE
                   WHEN u.phonenumber IS NOT NULL AND u.phonenumber != '' THEN
                       CONCAT(LEFT(u.phonenumber, 3), '****', RIGHT(u.phonenumber, 4))
                   ELSE ''
               END as publisher_phonenumber
        from app_task_application a
        left join app_task t on a.task_id = t.task_id
        left join app_user u on t.publisher_id = u.user_id
        left join app_user_profile up on t.publisher_id = up.user_id
        where a.applicant_id = #{applicantId}
        order by a.create_time desc
    </select>

    <select id="selectAppTaskApplicationListByTaskIdWithApplicantInfo" parameterType="Long" resultMap="AppTaskApplicationWithApplicantInfoResult">
        select a.application_id, a.task_id, a.applicant_id, a.applicant_name, a.applicant_avatar,
               a.application_reason, a.application_status, a.create_time, a.confirm_time, a.remark,
               up.credit_score as applicant_credit_score,
               u.sex as applicant_sex,
               CASE
                   WHEN u.real_name IS NOT NULL AND u.real_name != '' THEN
                       CONCAT(LEFT(u.real_name, 1), REPEAT('*', CHAR_LENGTH(u.real_name) - 1))
                   ELSE ''
               END as applicant_real_name,
               CASE
                   WHEN u.phonenumber IS NOT NULL AND u.phonenumber != '' THEN
                       CONCAT(LEFT(u.phonenumber, 3), '****', RIGHT(u.phonenumber, 4))
                   ELSE ''
               END as applicant_phonenumber
        from app_task_application a
        left join app_user u on a.applicant_id = u.user_id
        left join app_user_profile up on a.applicant_id = up.user_id
        where a.task_id = #{taskId}
        order by a.create_time desc
    </select>
        
    <insert id="insertAppTaskApplication" parameterType="AppTaskApplication" useGeneratedKeys="true" keyProperty="applicationId">
        insert into app_task_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="applicantId != null">applicant_id,</if>
            <if test="applicantName != null and applicantName != ''">applicant_name,</if>
            <if test="applicantAvatar != null and applicantAvatar != ''">applicant_avatar,</if>
            <if test="applicationReason != null and applicationReason != ''">application_reason,</if>
            <if test="applicationStatus != null and applicationStatus != ''">application_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="confirmTime != null">confirm_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="applicantId != null">#{applicantId},</if>
            <if test="applicantName != null and applicantName != ''">#{applicantName},</if>
            <if test="applicantAvatar != null and applicantAvatar != ''">#{applicantAvatar},</if>
            <if test="applicationReason != null and applicationReason != ''">#{applicationReason},</if>
            <if test="applicationStatus != null and applicationStatus != ''">#{applicationStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="confirmTime != null">#{confirmTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAppTaskApplication" parameterType="AppTaskApplication">
        update app_task_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="applicantId != null">applicant_id = #{applicantId},</if>
            <if test="applicantName != null and applicantName != ''">applicant_name = #{applicantName},</if>
            <if test="applicantAvatar != null and applicantAvatar != ''">applicant_avatar = #{applicantAvatar},</if>
            <if test="applicationReason != null and applicationReason != ''">application_reason = #{applicationReason},</if>
            <if test="applicationStatus != null and applicationStatus != ''">application_status = #{applicationStatus},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where application_id = #{applicationId}
    </update>

    <delete id="deleteAppTaskApplicationByApplicationId" parameterType="Long">
        delete from app_task_application where application_id = #{applicationId}
    </delete>

    <delete id="deleteAppTaskApplicationByApplicationIds" parameterType="String">
        delete from app_task_application where application_id in 
        <foreach item="applicationId" collection="array" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
    </delete>

    <update id="confirmApplication" parameterType="Long">
        update app_task_application 
        set application_status = '1', confirm_time = now()
        where application_id = #{applicationId}
    </update>

    <update id="rejectApplication" parameterType="Long">
        update app_task_application 
        set application_status = '2', confirm_time = now()
        where application_id = #{applicationId}
    </update>

    <update id="cancelApplication" parameterType="Long">
        update app_task_application 
        set application_status = '3'
        where application_id = #{applicationId}
    </update>

    <update id="rejectOtherApplicationsByTaskId">
        update app_task_application 
        set application_status = '2', confirm_time = now()
        where task_id = #{taskId} and application_id != #{confirmedApplicationId} and application_status = '0'
    </update>

</mapper>
