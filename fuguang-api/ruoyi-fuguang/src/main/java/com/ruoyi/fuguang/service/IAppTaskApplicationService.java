package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.AppTaskApplication;

/**
 * APP任务申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
public interface IAppTaskApplicationService 
{
    /**
     * 查询APP任务申请
     * 
     * @param applicationId APP任务申请主键
     * @return APP任务申请
     */
    public AppTaskApplication selectAppTaskApplicationByApplicationId(Long applicationId);

    /**
     * 查询APP任务申请列表
     * 
     * @param appTaskApplication APP任务申请
     * @return APP任务申请集合
     */
    public List<AppTaskApplication> selectAppTaskApplicationList(AppTaskApplication appTaskApplication);

    /**
     * 根据任务ID查询申请列表
     * 
     * @param taskId 任务ID
     * @return APP任务申请集合
     */
    public List<AppTaskApplication> selectAppTaskApplicationListByTaskId(Long taskId);

    /**
     * 根据申请人ID查询申请列表
     *
     * @param applicantId 申请人ID
     * @return APP任务申请集合
     */
    public List<AppTaskApplication> selectAppTaskApplicationListByApplicantId(Long applicantId);

    /**
     * 根据申请人ID查询申请列表（包含发布者详细信息）
     *
     * @param applicantId 申请人ID
     * @return APP任务申请集合
     */
    public List<AppTaskApplication> selectAppTaskApplicationListByApplicantIdWithPublisherInfo(Long applicantId);

    /**
     * 根据任务ID查询申请列表（包含申请人详细信息）
     *
     * @param taskId 任务ID
     * @return APP任务申请集合
     */
    public List<AppTaskApplication> selectAppTaskApplicationListByTaskIdWithApplicantInfo(Long taskId);

    /**
     * 申请任务
     * 
     * @param taskId 任务ID
     * @param applicantId 申请人ID
     * @param applicationReason 申请理由
     * @return 结果
     */
    public int applyForTask(Long taskId, Long applicantId, String applicationReason);

    /**
     * 新增APP任务申请
     * 
     * @param appTaskApplication APP任务申请
     * @return 结果
     */
    public int insertAppTaskApplication(AppTaskApplication appTaskApplication);

    /**
     * 修改APP任务申请
     * 
     * @param appTaskApplication APP任务申请
     * @return 结果
     */
    public int updateAppTaskApplication(AppTaskApplication appTaskApplication);

    /**
     * 批量删除APP任务申请
     * 
     * @param applicationIds 需要删除的APP任务申请主键集合
     * @return 结果
     */
    public int deleteAppTaskApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 删除APP任务申请信息
     * 
     * @param applicationId APP任务申请主键
     * @return 结果
     */
    public int deleteAppTaskApplicationByApplicationId(Long applicationId);

    /**
     * 确认申请（发布者选择接取人）
     * 
     * @param applicationId 申请ID
     * @param publisherId 发布者ID（用于权限验证）
     * @return 结果
     */
    public int confirmApplication(Long applicationId, Long publisherId);

    /**
     * 拒绝申请
     * 
     * @param applicationId 申请ID
     * @param publisherId 发布者ID（用于权限验证）
     * @return 结果
     */
    public int rejectApplication(Long applicationId, Long publisherId);

    /**
     * 取消申请（申请人取消自己的申请）
     * 
     * @param applicationId 申请ID
     * @param applicantId 申请人ID（用于权限验证）
     * @return 结果
     */
    public int cancelApplication(Long applicationId, Long applicantId);

    /**
     * 检查用户是否已申请该任务
     * 
     * @param taskId 任务ID
     * @param applicantId 申请人ID
     * @return 是否已申请
     */
    public boolean hasAppliedForTask(Long taskId, Long applicantId);
}
