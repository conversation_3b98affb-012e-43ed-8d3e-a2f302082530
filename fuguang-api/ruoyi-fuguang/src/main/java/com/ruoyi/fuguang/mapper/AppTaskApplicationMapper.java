package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.AppTaskApplication;
import org.apache.ibatis.annotations.Param;

/**
 * APP任务申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
public interface AppTaskApplicationMapper 
{
    /**
     * 查询APP任务申请
     * 
     * @param applicationId APP任务申请主键
     * @return APP任务申请
     */
    public AppTaskApplication selectAppTaskApplicationByApplicationId(Long applicationId);

    /**
     * 查询APP任务申请列表
     * 
     * @param appTaskApplication APP任务申请
     * @return APP任务申请集合
     */
    public List<AppTaskApplication> selectAppTaskApplicationList(AppTaskApplication appTaskApplication);

    /**
     * 根据任务ID查询申请列表
     * 
     * @param taskId 任务ID
     * @return APP任务申请集合
     */
    public List<AppTaskApplication> selectAppTaskApplicationListByTaskId(Long taskId);

    /**
     * 根据申请人ID查询申请列表
     *
     * @param applicantId 申请人ID
     * @return APP任务申请集合
     */
    public List<AppTaskApplication> selectAppTaskApplicationListByApplicantId(Long applicantId);

    /**
     * 根据申请人ID查询申请列表（包含发布者详细信息）
     *
     * @param applicantId 申请人ID
     * @return APP任务申请集合
     */
    public List<AppTaskApplication> selectAppTaskApplicationListByApplicantIdWithPublisherInfo(Long applicantId);

    /**
     * 根据任务ID查询申请列表（包含申请人详细信息）
     *
     * @param taskId 任务ID
     * @return APP任务申请集合
     */
    public List<AppTaskApplication> selectAppTaskApplicationListByTaskIdWithApplicantInfo(Long taskId);

    /**
     * 检查用户是否已申请该任务
     * 
     * @param taskId 任务ID
     * @param applicantId 申请人ID
     * @return 申请记录
     */
    public AppTaskApplication selectAppTaskApplicationByTaskIdAndApplicantId(@Param("taskId") Long taskId, @Param("applicantId") Long applicantId);

    /**
     * 新增APP任务申请
     * 
     * @param appTaskApplication APP任务申请
     * @return 结果
     */
    public int insertAppTaskApplication(AppTaskApplication appTaskApplication);

    /**
     * 修改APP任务申请
     * 
     * @param appTaskApplication APP任务申请
     * @return 结果
     */
    public int updateAppTaskApplication(AppTaskApplication appTaskApplication);

    /**
     * 删除APP任务申请
     * 
     * @param applicationId APP任务申请主键
     * @return 结果
     */
    public int deleteAppTaskApplicationByApplicationId(Long applicationId);

    /**
     * 批量删除APP任务申请
     * 
     * @param applicationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppTaskApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 确认申请（更新申请状态为已确认）
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    public int confirmApplication(Long applicationId);

    /**
     * 拒绝申请（更新申请状态为已拒绝）
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    public int rejectApplication(Long applicationId);

    /**
     * 取消申请（更新申请状态为已取消）
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    public int cancelApplication(Long applicationId);

    /**
     * 根据任务ID拒绝所有其他申请（除了指定的申请ID）
     * 
     * @param taskId 任务ID
     * @param confirmedApplicationId 已确认的申请ID
     * @return 结果
     */
    public int rejectOtherApplicationsByTaskId(@Param("taskId") Long taskId, @Param("confirmedApplicationId") Long confirmedApplicationId);
}
