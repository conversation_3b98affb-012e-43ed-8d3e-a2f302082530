package com.ruoyi.fuguang.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP任务申请对象 app_task_application
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppTaskApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请ID */
    private Long applicationId;

    /** 任务ID */
    @Excel(name = "任务ID")
    private Long taskId;

    /** 申请人ID */
    @Excel(name = "申请人ID")
    private Long applicantId;

    /** 申请人昵称 */
    @Excel(name = "申请人昵称")
    private String applicantName;

    /** 申请人头像 */
    private String applicantAvatar;

    /** 申请理由 */
    @Excel(name = "申请理由")
    private String applicationReason;

    /** 申请状态（0待确认 1已确认 2已拒绝 3已取消） */
    @Excel(name = "申请状态", readConverterExp = "0=待确认,1=已确认,2=已拒绝,3=已取消")
    private String applicationStatus;

    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date confirmTime;

    /** 任务信息（用于返回给前端，不存储到数据库） */
    private AppTask task;

    /** 申请人个人简介（用于返回给前端，不存储到数据库） */
    private AppUserProfile applicantUserProfile;

    /** 发布者个人简介（用于返回给前端，不存储到数据库） */
    private AppUserProfile publisherUserProfile;
}
