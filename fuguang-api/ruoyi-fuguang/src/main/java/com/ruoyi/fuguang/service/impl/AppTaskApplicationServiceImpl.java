package com.ruoyi.fuguang.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.mapper.AppTaskApplicationMapper;
import com.ruoyi.fuguang.mapper.AppTaskMapper;
import com.ruoyi.fuguang.domain.AppTaskApplication;
import com.ruoyi.fuguang.domain.AppTask;
import com.ruoyi.fuguang.domain.AppUser;
import com.ruoyi.fuguang.service.IAppTaskApplicationService;
import com.ruoyi.fuguang.service.IAppUserService;

/**
 * APP任务申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class AppTaskApplicationServiceImpl implements IAppTaskApplicationService 
{
    @Autowired
    private AppTaskApplicationMapper appTaskApplicationMapper;

    @Autowired
    private AppTaskMapper appTaskMapper;

    @Autowired
    private IAppUserService appUserService;

    /**
     * 查询APP任务申请
     * 
     * @param applicationId APP任务申请主键
     * @return APP任务申请
     */
    @Override
    public AppTaskApplication selectAppTaskApplicationByApplicationId(Long applicationId)
    {
        return appTaskApplicationMapper.selectAppTaskApplicationByApplicationId(applicationId);
    }

    /**
     * 查询APP任务申请列表
     * 
     * @param appTaskApplication APP任务申请
     * @return APP任务申请
     */
    @Override
    public List<AppTaskApplication> selectAppTaskApplicationList(AppTaskApplication appTaskApplication)
    {
        return appTaskApplicationMapper.selectAppTaskApplicationList(appTaskApplication);
    }

    /**
     * 根据任务ID查询申请列表
     * 
     * @param taskId 任务ID
     * @return APP任务申请集合
     */
    @Override
    public List<AppTaskApplication> selectAppTaskApplicationListByTaskId(Long taskId)
    {
        return appTaskApplicationMapper.selectAppTaskApplicationListByTaskId(taskId);
    }

    /**
     * 根据申请人ID查询申请列表
     *
     * @param applicantId 申请人ID
     * @return APP任务申请集合
     */
    @Override
    public List<AppTaskApplication> selectAppTaskApplicationListByApplicantId(Long applicantId)
    {
        return appTaskApplicationMapper.selectAppTaskApplicationListByApplicantId(applicantId);
    }

    /**
     * 根据申请人ID查询申请列表（包含发布者详细信息）
     *
     * @param applicantId 申请人ID
     * @return APP任务申请集合
     */
    @Override
    public List<AppTaskApplication> selectAppTaskApplicationListByApplicantIdWithPublisherInfo(Long applicantId)
    {
        return appTaskApplicationMapper.selectAppTaskApplicationListByApplicantIdWithPublisherInfo(applicantId);
    }

    /**
     * 根据任务ID查询申请列表（包含申请人详细信息）
     *
     * @param taskId 任务ID
     * @return APP任务申请集合
     */
    @Override
    public List<AppTaskApplication> selectAppTaskApplicationListByTaskIdWithApplicantInfo(Long taskId)
    {
        return appTaskApplicationMapper.selectAppTaskApplicationListByTaskIdWithApplicantInfo(taskId);
    }

    /**
     * 申请任务
     * 
     * @param taskId 任务ID
     * @param applicantId 申请人ID
     * @param applicationReason 申请理由
     * @return 结果
     */
    @Override
    @Transactional
    public int applyForTask(Long taskId, Long applicantId, String applicationReason)
    {
        // 验证任务是否存在
        AppTask task = appTaskMapper.selectAppTaskByTaskId(taskId);
        if (task == null) {
            throw new ServiceException("任务不存在");
        }

        // 验证任务状态是否允许申请（只有待接取状态才能申请）
        if (!"1".equals(task.getTaskStatus())) {
            throw new ServiceException("任务状态不允许申请");
        }

        // 验证是否为任务发布者（不能申请自己发布的任务）
        if (task.getPublisherId().equals(applicantId)) {
            throw new ServiceException("不能申请自己发布的任务");
        }

        // 验证是否已经申请过
        AppTaskApplication existingApplication = appTaskApplicationMapper.selectAppTaskApplicationByTaskIdAndApplicantId(taskId, applicantId);
        if (existingApplication != null) {
            throw new ServiceException("您已经申请过该任务");
        }

        // 获取申请人信息
        AppUser applicant = appUserService.selectAppUserByUserId(applicantId);
        if (applicant == null) {
            throw new ServiceException("申请人不存在");
        }

        // 创建申请记录
        AppTaskApplication application = new AppTaskApplication();
        application.setTaskId(taskId);
        application.setApplicantId(applicantId);
        application.setApplicantName(applicant.getNickName());
        application.setApplicantAvatar(applicant.getAvatar());
        application.setApplicationReason(applicationReason);
        application.setApplicationStatus("0"); // 待确认
        application.setCreateTime(DateUtils.getNowDate());

        int result = appTaskApplicationMapper.insertAppTaskApplication(application);

        // 如果申请成功，更新任务状态为"待确认接取人"
        if (result > 0) {
            appTaskMapper.updateTaskStatus(taskId, "6"); // 6-待确认接取人
        }

        return result;
    }

    /**
     * 新增APP任务申请
     * 
     * @param appTaskApplication APP任务申请
     * @return 结果
     */
    @Override
    public int insertAppTaskApplication(AppTaskApplication appTaskApplication)
    {
        appTaskApplication.setCreateTime(DateUtils.getNowDate());
        return appTaskApplicationMapper.insertAppTaskApplication(appTaskApplication);
    }

    /**
     * 修改APP任务申请
     * 
     * @param appTaskApplication APP任务申请
     * @return 结果
     */
    @Override
    public int updateAppTaskApplication(AppTaskApplication appTaskApplication)
    {
        return appTaskApplicationMapper.updateAppTaskApplication(appTaskApplication);
    }

    /**
     * 批量删除APP任务申请
     * 
     * @param applicationIds 需要删除的APP任务申请主键
     * @return 结果
     */
    @Override
    public int deleteAppTaskApplicationByApplicationIds(Long[] applicationIds)
    {
        return appTaskApplicationMapper.deleteAppTaskApplicationByApplicationIds(applicationIds);
    }

    /**
     * 删除APP任务申请信息
     * 
     * @param applicationId APP任务申请主键
     * @return 结果
     */
    @Override
    public int deleteAppTaskApplicationByApplicationId(Long applicationId)
    {
        return appTaskApplicationMapper.deleteAppTaskApplicationByApplicationId(applicationId);
    }

    /**
     * 确认申请（发布者选择接取人）
     * 
     * @param applicationId 申请ID
     * @param publisherId 发布者ID（用于权限验证）
     * @return 结果
     */
    @Override
    @Transactional
    public int confirmApplication(Long applicationId, Long publisherId)
    {
        // 获取申请信息
        AppTaskApplication application = appTaskApplicationMapper.selectAppTaskApplicationByApplicationId(applicationId);
        if (application == null) {
            throw new ServiceException("申请不存在");
        }

        // 获取任务信息
        AppTask task = appTaskMapper.selectAppTaskByTaskId(application.getTaskId());
        if (task == null) {
            throw new ServiceException("任务不存在");
        }

        // 验证权限（只有发布者可以确认，管理员publisherId为0时跳过权限检查）
        if (publisherId != 0L && !task.getPublisherId().equals(publisherId)) {
            throw new ServiceException("无权限操作");
        }

        // 验证申请状态
        if (!"0".equals(application.getApplicationStatus())) {
            throw new ServiceException("申请状态不允许确认");
        }

        // 确认申请
        int result = appTaskApplicationMapper.confirmApplication(applicationId);

        if (result > 0) {
            // 更新任务状态为进行中，并设置接收者
            appTaskMapper.acceptTask(application.getTaskId(), application.getApplicantId(), application.getApplicantName());
            
            // 拒绝该任务的其他所有申请
            appTaskApplicationMapper.rejectOtherApplicationsByTaskId(application.getTaskId(), applicationId);
        }

        return result;
    }

    /**
     * 拒绝申请
     * 
     * @param applicationId 申请ID
     * @param publisherId 发布者ID（用于权限验证）
     * @return 结果
     */
    @Override
    public int rejectApplication(Long applicationId, Long publisherId)
    {
        // 获取申请信息
        AppTaskApplication application = appTaskApplicationMapper.selectAppTaskApplicationByApplicationId(applicationId);
        if (application == null) {
            throw new ServiceException("申请不存在");
        }

        // 获取任务信息
        AppTask task = appTaskMapper.selectAppTaskByTaskId(application.getTaskId());
        if (task == null) {
            throw new ServiceException("任务不存在");
        }

        // 验证权限（只有发布者可以拒绝，管理员publisherId为0时跳过权限检查）
        if (publisherId != 0L && !task.getPublisherId().equals(publisherId)) {
            throw new ServiceException("无权限操作");
        }

        // 验证申请状态
        if (!"0".equals(application.getApplicationStatus())) {
            throw new ServiceException("申请状态不允许拒绝");
        }

        return appTaskApplicationMapper.rejectApplication(applicationId);
    }

    /**
     * 取消申请（申请人取消自己的申请）
     * 
     * @param applicationId 申请ID
     * @param applicantId 申请人ID（用于权限验证）
     * @return 结果
     */
    @Override
    public int cancelApplication(Long applicationId, Long applicantId)
    {
        // 获取申请信息
        AppTaskApplication application = appTaskApplicationMapper.selectAppTaskApplicationByApplicationId(applicationId);
        if (application == null) {
            throw new ServiceException("申请不存在");
        }

        // 验证权限（只有申请人可以取消）
        if (!application.getApplicantId().equals(applicantId)) {
            throw new ServiceException("无权限操作");
        }

        // 验证申请状态
        if (!"0".equals(application.getApplicationStatus())) {
            throw new ServiceException("申请状态不允许取消");
        }

        return appTaskApplicationMapper.cancelApplication(applicationId);
    }

    /**
     * 检查用户是否已申请该任务
     * 
     * @param taskId 任务ID
     * @param applicantId 申请人ID
     * @return 是否已申请
     */
    @Override
    public boolean hasAppliedForTask(Long taskId, Long applicantId)
    {
        AppTaskApplication application = appTaskApplicationMapper.selectAppTaskApplicationByTaskIdAndApplicantId(taskId, applicantId);
        return application != null;
    }
}
