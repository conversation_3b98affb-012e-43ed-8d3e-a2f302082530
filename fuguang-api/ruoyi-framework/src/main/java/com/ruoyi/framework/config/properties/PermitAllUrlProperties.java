package com.ruoyi.framework.config.properties;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;
import org.apache.commons.lang3.RegExUtils;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import com.ruoyi.common.annotation.Anonymous;

/**
 * 设置Anonymous注解允许匿名访问的url
 *
 * <AUTHOR>
 */
@Configuration
public class PermitAllUrlProperties implements ApplicationListener<ApplicationReadyEvent>, ApplicationContextAware
{
    private static final Pattern PATTERN = Pattern.compile("\\{(.*?)\\}");

    private ApplicationContext applicationContext;

    private List<String> urls = new ArrayList<>();

    public String ASTERISK = "*";

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event)
    {
        try {
            RequestMappingHandlerMapping mapping = applicationContext.getBean(RequestMappingHandlerMapping.class);
            Map<RequestMappingInfo, HandlerMethod> map = mapping.getHandlerMethods();

            map.keySet().forEach(info -> {
                HandlerMethod handlerMethod = map.get(info);

                // 获取方法上边的注解 替代path variable 为 *
                Anonymous method = AnnotationUtils.findAnnotation(handlerMethod.getMethod(), Anonymous.class);
                Optional.ofNullable(method).ifPresent(anonymous -> {
                    Objects.requireNonNull(info.getPatternsCondition().getPatterns())
                            .forEach(url -> {
                                String processedUrl = RegExUtils.replaceAll(url, PATTERN, ASTERISK);
                                urls.add(processedUrl);
                                System.out.println("添加Anonymous方法URL: " + processedUrl + " (原始: " + url + ")");
                            });
                });

                // 获取类上边的注解, 替代path variable 为 *
                Anonymous controller = AnnotationUtils.findAnnotation(handlerMethod.getBeanType(), Anonymous.class);
                Optional.ofNullable(controller).ifPresent(anonymous -> {
                    Objects.requireNonNull(info.getPatternsCondition().getPatterns())
                            .forEach(url -> {
                                String processedUrl = RegExUtils.replaceAll(url, PATTERN, ASTERISK);
                                urls.add(processedUrl);
                                System.out.println("添加Anonymous类URL: " + processedUrl + " (原始: " + url + ")");
                            });
                });
            });

            System.out.println("Anonymous注解收集完成，共收集到 " + urls.size() + " 个URL:");
            urls.forEach(url -> System.out.println("  - " + url));

        } catch (Exception e) {
            // 如果获取失败，记录日志但不影响启动
            System.err.println("Failed to initialize PermitAllUrlProperties: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException
    {
        this.applicationContext = context;
    }

    public List<String> getUrls()
    {
        return urls;
    }

    public void setUrls(List<String> urls)
    {
        this.urls = urls;
    }
}
