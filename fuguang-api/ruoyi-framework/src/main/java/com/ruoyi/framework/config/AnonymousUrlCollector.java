package com.ruoyi.framework.config;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import org.apache.commons.lang3.RegExUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import com.ruoyi.common.annotation.Anonymous;

/**
 * Anonymous注解URL收集器
 * 在RequestMappingHandlerMapping初始化后收集所有@Anonymous注解的URL
 * 
 * <AUTHOR>
 */
@Component
public class AnonymousUrlCollector implements BeanPostProcessor
{
    private static final Pattern PATTERN = Pattern.compile("\\{(.*?)\\}");
    private static final String ASTERISK = "*";
    private static final List<String> ANONYMOUS_URLS = new ArrayList<>();
    
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException
    {
        if (bean instanceof RequestMappingHandlerMapping)
        {
            RequestMappingHandlerMapping mapping = (RequestMappingHandlerMapping) bean;
            collectAnonymousUrls(mapping);
        }
        return bean;
    }
    
    /**
     * 收集所有@Anonymous注解的URL
     */
    private void collectAnonymousUrls(RequestMappingHandlerMapping mapping)
    {
        System.out.println("AnonymousUrlCollector: 开始收集Anonymous注解URL...");
        
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = mapping.getHandlerMethods();
        
        for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethods.entrySet())
        {
            RequestMappingInfo mappingInfo = entry.getKey();
            HandlerMethod handlerMethod = entry.getValue();
            
            // 检查方法上的@Anonymous注解
            Method method = handlerMethod.getMethod();
            Anonymous methodAnnotation = AnnotationUtils.findAnnotation(method, Anonymous.class);
            
            // 检查类上的@Anonymous注解
            Class<?> beanType = handlerMethod.getBeanType();
            Anonymous classAnnotation = AnnotationUtils.findAnnotation(beanType, Anonymous.class);
            
            if (methodAnnotation != null || classAnnotation != null)
            {
                // 获取URL模式
                if (mappingInfo.getPatternsCondition() != null && 
                    mappingInfo.getPatternsCondition().getPatterns() != null)
                {
                    for (String pattern : mappingInfo.getPatternsCondition().getPatterns())
                    {
                        String processedUrl = RegExUtils.replaceAll(pattern, PATTERN, ASTERISK);
                        ANONYMOUS_URLS.add(processedUrl);
                        
                        String annotationType = methodAnnotation != null ? "方法" : "类";
                        System.out.println("收集到Anonymous " + annotationType + " URL: " + processedUrl + 
                                         " (原始: " + pattern + ") - " + 
                                         beanType.getSimpleName() + "." + method.getName());
                    }
                }
            }
        }
        
        System.out.println("AnonymousUrlCollector: 收集完成，共找到 " + ANONYMOUS_URLS.size() + " 个Anonymous URL");
        ANONYMOUS_URLS.forEach(url -> System.out.println("  - " + url));
    }
    
    /**
     * 获取收集到的Anonymous URL列表
     */
    public static List<String> getAnonymousUrls()
    {
        return new ArrayList<>(ANONYMOUS_URLS);
    }
    
    /**
     * 获取Anonymous URL数组，用于Spring Security配置
     */
    public static String[] getAnonymousUrlArray()
    {
        return ANONYMOUS_URLS.toArray(new String[0]);
    }
}
