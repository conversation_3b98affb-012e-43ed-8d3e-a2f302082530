package com.ruoyi.framework.security.handle;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Map;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;

/**
 * 自定义认证失败处理类，支持Anonymous注解
 * 
 * <AUTHOR>
 */
@Component("anonymousAuthenticationEntryPoint")
public class AnonymousAuthenticationEntryPoint implements AuthenticationEntryPoint
{
    @Autowired
    private RequestMappingHandlerMapping requestMappingHandlerMapping;
    
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e)
            throws IOException, ServletException
    {
        String requestURI = request.getRequestURI();
        String httpMethod = request.getMethod();
        
        // 检查当前请求是否匹配@Anonymous注解的接口
        if (isAnonymousAccess(requestURI, httpMethod))
        {
            System.out.println("Anonymous访问检测: " + requestURI + " [" + httpMethod + "] - 允许匿名访问，跳过认证");
            // 如果是Anonymous接口，直接放行，不返回401
            return;
        }
        
        // 非Anonymous接口，返回401未授权
        int code = HttpStatus.UNAUTHORIZED;
        String msg = StringUtils.format("请求访问：{}，认证失败，无法访问系统资源", requestURI);
        ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(code, msg)));
    }
    
    /**
     * 检查请求是否匹配@Anonymous注解的接口
     */
    private boolean isAnonymousAccess(String requestURI, String httpMethod)
    {
        try
        {
            Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();
            
            for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethods.entrySet())
            {
                RequestMappingInfo mappingInfo = entry.getKey();
                HandlerMethod handlerMethod = entry.getValue();
                
                // 检查URL是否匹配
                if (mappingInfo.getPatternsCondition() != null && 
                    mappingInfo.getPatternsCondition().getPatterns() != null)
                {
                    for (String pattern : mappingInfo.getPatternsCondition().getPatterns())
                    {
                        if (pathMatcher.match(pattern, requestURI))
                        {
                            // 检查HTTP方法是否匹配
                            if (mappingInfo.getMethodsCondition().getMethods().isEmpty() ||
                                mappingInfo.getMethodsCondition().getMethods().stream()
                                    .anyMatch(m -> m.name().equals(httpMethod)))
                            {
                                // 检查方法或类上是否有@Anonymous注解
                                Method targetMethod = handlerMethod.getMethod();
                                Class<?> targetClass = handlerMethod.getBeanType();
                                
                                if (AnnotationUtils.findAnnotation(targetMethod, Anonymous.class) != null ||
                                    AnnotationUtils.findAnnotation(targetClass, Anonymous.class) != null)
                                {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.err.println("检查Anonymous注解时发生错误: " + ex.getMessage());
        }
        
        return false;
    }
}
