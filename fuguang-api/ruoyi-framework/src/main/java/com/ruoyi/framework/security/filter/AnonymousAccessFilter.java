package com.ruoyi.framework.security.filter;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Map;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import com.ruoyi.common.annotation.Anonymous;

/**
 * Anonymous注解访问过滤器
 * 检查请求的接口是否标记了@Anonymous注解，如果是则跳过认证
 * 
 * <AUTHOR>
 */
@Component
public class AnonymousAccessFilter extends OncePerRequestFilter
{
    @Autowired
    private RequestMappingHandlerMapping requestMappingHandlerMapping;
    
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException
    {
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        
        // 检查当前请求是否匹配@Anonymous注解的接口
        if (isAnonymousAccess(requestURI, method))
        {
            System.out.println("Anonymous访问检测: " + requestURI + " [" + method + "] - 允许匿名访问");
            // 清除当前的认证信息，确保以匿名方式处理
            SecurityContextHolder.clearContext();
        }
        
        filterChain.doFilter(request, response);
    }
    
    /**
     * 检查请求是否匹配@Anonymous注解的接口
     */
    private boolean isAnonymousAccess(String requestURI, String httpMethod)
    {
        try
        {
            Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();
            
            for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethods.entrySet())
            {
                RequestMappingInfo mappingInfo = entry.getKey();
                HandlerMethod handlerMethod = entry.getValue();
                
                // 检查URL是否匹配
                if (mappingInfo.getPatternsCondition() != null && 
                    mappingInfo.getPatternsCondition().getPatterns() != null)
                {
                    for (String pattern : mappingInfo.getPatternsCondition().getPatterns())
                    {
                        if (pathMatcher.match(pattern, requestURI))
                        {
                            // 检查HTTP方法是否匹配
                            if (mappingInfo.getMethodsCondition().getMethods().isEmpty() ||
                                mappingInfo.getMethodsCondition().getMethods().stream()
                                    .anyMatch(m -> m.name().equals(httpMethod)))
                            {
                                // 检查方法或类上是否有@Anonymous注解
                                Method targetMethod = handlerMethod.getMethod();
                                Class<?> targetClass = handlerMethod.getBeanType();
                                
                                if (AnnotationUtils.findAnnotation(targetMethod, Anonymous.class) != null ||
                                    AnnotationUtils.findAnnotation(targetClass, Anonymous.class) != null)
                                {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        catch (Exception e)
        {
            System.err.println("检查Anonymous注解时发生错误: " + e.getMessage());
        }
        
        return false;
    }
}
