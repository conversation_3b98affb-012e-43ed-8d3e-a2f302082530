# 任务申请用户信息接口文档

## 功能概述

本文档描述了两个增强的任务申请接口，这些接口现在会返回用户的详细信息，包括信用分、性别、实名姓名（脱敏）和手机号（脱敏）。

## 接口列表

### 1. 获取当前用户的所有任务申请记录

**接口路径：** `GET /app/task/applications/my`

**功能描述：** 获取当前用户的所有任务申请记录，包含发布用户的信用分、性别、实名姓名（脱敏）、用户手机号（脱敏）

**请求参数：** 无（从token中获取用户ID）

**返回数据结构：**
```json
{
  "total": 10,
  "rows": [
    {
      "applicationId": 1001,
      "taskId": 2001,
      "applicantId": 1000,
      "applicantName": "张三",
      "applicantAvatar": "/avatar/zhang.jpg",
      "applicationReason": "我有相关经验，可以很好地完成这个任务",
      "applicationStatus": "0",
      "createTime": "2025-01-29 10:30:00",
      "confirmTime": null,
      "task": {
        "taskId": 2001,
        "taskTitle": "帮忙搬家",
        "taskDesc": "需要帮忙搬家到新地址",
        "taskAmount": 100.00,
        "taskStatus": "6",
        "publisherId": 1001,
        "publisherName": "李四",
        "taskAddress": "北京市朝阳区",
        "startTime": "2025-01-30 09:00:00",
        "endTime": "2025-01-30 17:00:00",
        "publisherUserProfile": {
          "creditScore": 95,
          "sex": "0",
          "realName": "李*",
          "phonenumber": "138****5678"
        }
      }
    }
  ],
  "code": 200,
  "msg": "查询成功"
}
```

### 2. 发布者查看自己发布任务的申请情况

**接口路径：** `GET /app/task/applications/{taskId}`

**功能描述：** 发布者查看自己发布任务的申请情况，包含申请用户的信用分、性别、实名姓名（脱敏）、用户手机号（脱敏）

**请求参数：**
- `taskId` (路径参数): 任务ID

**权限验证：** 只有任务发布者可以查看申请列表

**返回数据结构：**
```json
{
  "total": 5,
  "rows": [
    {
      "applicationId": 1001,
      "taskId": 2001,
      "applicantId": 1000,
      "applicantName": "张三",
      "applicantAvatar": "/avatar/zhang.jpg",
      "applicationReason": "我有相关经验，可以很好地完成这个任务",
      "applicationStatus": "0",
      "createTime": "2025-01-29 10:30:00",
      "confirmTime": null,
      "applicantUserProfile": {
        "creditScore": 100,
        "sex": "0",
        "realName": "张*",
        "phonenumber": "139****1234"
      }
    },
    {
      "applicationId": 1002,
      "taskId": 2001,
      "applicantId": 1002,
      "applicantName": "王五",
      "applicantAvatar": "/avatar/wang.jpg",
      "applicationReason": "时间充裕，可以随时开始",
      "applicationStatus": "0",
      "createTime": "2025-01-29 11:15:00",
      "confirmTime": null,
      "applicantUserProfile": {
        "creditScore": 105,
        "sex": "1",
        "realName": "王*",
        "phonenumber": "186****9876"
      }
    }
  ],
  "code": 200,
  "msg": "查询成功"
}
```

## 数据字段说明

### 用户信息字段（UserProfile）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| creditScore | Integer | 用户信用分 |
| sex | String | 用户性别（0=男，1=女，2=未知） |
| realName | String | 实名姓名（脱敏处理，只显示第一个字符，其余用*代替） |
| phonenumber | String | 手机号码（脱敏处理，显示前3位和后4位，中间用****代替） |

### 申请状态说明

| 状态值 | 说明 |
|--------|------|
| 0 | 待确认 |
| 1 | 已确认 |
| 2 | 已拒绝 |
| 3 | 已取消 |

## 脱敏规则

### 姓名脱敏
- 规则：显示第一个字符，其余字符用*代替
- 示例：张三 → 张*，李小明 → 李**

### 手机号脱敏
- 规则：显示前3位和后4位，中间用****代替
- 示例：13812345678 → 138****5678

## 错误处理

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| 500 | 无权限查看或任务不存在 |
| 401 | 用户未登录 |

### 错误响应示例

```json
{
  "code": 500,
  "msg": "无权限查看此任务的申请列表"
}
```

## 注意事项

1. **权限控制**：第二个接口只有任务发布者才能访问
2. **数据脱敏**：所有用户敏感信息都经过脱敏处理
3. **分页支持**：两个接口都支持分页查询
4. **实时数据**：返回的信用分等信息都是实时的最新数据

## 技术实现

### 数据库查询优化
- 使用LEFT JOIN关联用户表和用户资料表
- 在SQL层面进行数据脱敏处理，提高性能
- 添加适当的索引以优化查询性能

### 安全考虑
- 严格的权限验证
- 敏感信息脱敏处理
- 防止SQL注入等安全问题
