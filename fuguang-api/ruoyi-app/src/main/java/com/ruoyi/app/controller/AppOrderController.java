package com.ruoyi.app.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.math.BigDecimal;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.domain.MallPayment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.fuguang.domain.MallOrder;
import com.ruoyi.fuguang.domain.MallOrderItem;
import com.ruoyi.fuguang.service.IMallOrderService;
import com.ruoyi.fuguang.service.IMallPaymentService;
import com.ruoyi.app.domain.CreateOrderRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

import static com.ruoyi.common.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * APP订单接口控制器
 * 提供订单创建、查询、支付、取消等功能
 *
 * <AUTHOR>
 */
@Api(tags = "APP商城订单接口", description = "APP商城订单管理相关接口")
@RestController("appOrderController")
@RequestMapping("/app/order")
public class AppOrderController extends BaseController
{
    @Autowired
    private IMallOrderService mallOrderService;


    @Autowired
    private IMallPaymentService mallPaymentService;

    /**
     * 获取当前用户订单列表
     * 支持按订单状态筛选，支持分页
     *
     * @param orderStatus 订单状态，可选（0-待支付，1-已支付，2-已发货，3-已完成，4-已取消）
     * @return 订单列表
     */
    @ApiOperation(value = "获取订单列表",
                  notes = "获取当前用户的订单列表，支持按状态筛选：0-待支付，1-已支付，2-已发货，3-已完成，4-已取消")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回订单列表")
    })
    @GetMapping("/list")
    public TableDataInfo getOrderList(@ApiParam(value = "订单状态", required = false) @RequestParam(required = false) String orderStatus)
    {
        Long userId = getUserId();
        startPage();
        List<MallOrder> list = mallOrderService.selectOrderListByUserId(userId, orderStatus);
        return getDataTable(list);
    }

    /**
     * 获取订单详情
     * 根据订单ID获取订单详细信息，包含订单项
     *
     * @param orderId 订单ID
     * @return 订单详细信息
     */
    @ApiOperation(value = "获取订单详情",
                  notes = "根据订单ID获取订单详细信息，包含订单项、收货地址等信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回订单详情"),
        @ApiResponse(code = 500, message = "订单不存在或无权访问")
    })
    @GetMapping("/{orderId}")
    public AjaxResult getOrderDetail(@ApiParam(value = "订单ID", required = true) @PathVariable Long orderId)
    {
        Long userId = getUserId();
        MallOrder order = mallOrderService.selectMallOrderByOrderId(orderId);

        if (order == null) {
            return error("订单不存在");
        }

        if (!order.getUserId().equals(userId)) {
            return error("无权访问此订单");
        }

        return success(order);
    }

    /**
     * 创建订单
     * 根据购物车商品和收货地址创建订单
     *
     * @param request 创建订单请求，包含商品信息、收货地址、配送费等
     * @return 创建的订单信息
     */
    @ApiOperation(value = "创建订单",
                  notes = "根据商品信息和收货地址创建订单，订单创建后状态为待支付")
    @ApiResponses({
        @ApiResponse(code = 200, message = "创建成功，返回订单信息"),
        @ApiResponse(code = 500, message = "创建失败，参数错误或商品库存不足")
    })
    @PostMapping("/create")
    public AjaxResult createOrder(@ApiParam(value = "创建订单请求", required = true) @RequestBody CreateOrderRequest request)
    {
        try {
            // 参数验证
            if (request.getAddressId() == null) {
                return error("请选择收货地址");
            }
            if (request.getOrderItems() == null || request.getOrderItems().isEmpty()) {
                return error("订单商品不能为空");
            }
            // 转换为订单对象
            MallOrder mallOrder = new MallOrder();
            mallOrder.setAddressId(request.getAddressId());
            mallOrder.setDeliveryFee(request.getDeliveryFee() != null ? request.getDeliveryFee() : new BigDecimal("0"));
            mallOrder.setRemark(request.getRemark());
            // 转换订单项
            List<MallOrderItem> orderItems = new ArrayList<>();
            for (CreateOrderRequest.CreateOrderItem item : request.getOrderItems()) {
                if (item.getProductId() == null || item.getQuantity() == null || item.getQuantity() <= 0) {
                    return error("商品信息不完整");
                }
                MallOrderItem orderItem = new MallOrderItem();
                orderItem.setProductId(item.getProductId());
                orderItem.setSpecId(item.getSpecId());
                orderItem.setQuantity(item.getQuantity());
                orderItems.add(orderItem);
            }
            mallOrder.setOrderItems(orderItems);
            MallOrder order = mallOrderService.createOrder(mallOrder);
            return success(order);
        } catch (Exception e) {
            return error("订单创建失败：" + e.getMessage());
        }
    }

    /**
     * 取消订单
     */
    @ApiOperation("取消订单")
    @PutMapping("/{orderId}/cancel")
    public AjaxResult cancelOrder(@ApiParam("订单ID") @PathVariable Long orderId)
    {
        Long userId = getUserId();
        int result = mallOrderService.cancelOrder(orderId, userId);
        
        if (result > 0) {
            return success("订单取消成功");
        } else {
            return error("订单取消失败");
        }
    }

    /**
     * 确认收货
     */
    @ApiOperation("确认收货")
    @PutMapping("/{orderId}/confirm")
    public AjaxResult confirmReceive(@ApiParam("订单ID") @PathVariable Long orderId)
    {
        Long userId = getUserId();
        int result = mallOrderService.confirmReceive(orderId, userId);
        if (result > 0) {
            return success("确认收货成功");
        } else {
            return error("确认收货失败");
        }
    }

    /**
     * 创建商城支付订单
     */
    @ApiOperation("创建商城支付订单")
    @PostMapping("/{orderId}/pay")
    public AjaxResult createPayment(
            @ApiParam("订单ID") @PathVariable Long orderId,
            @ApiParam("支付方式") @RequestParam(defaultValue = "1") String payType)
    {
        Long userId = getUserId();
        MallOrder order = mallOrderService.selectMallOrderByOrderId(orderId);
        if (order == null) {
            return error("订单不存在");
        }
        if (!order.getUserId().equals(userId)) {
            return error("无权访问此订单");
        }
        if (!"0".equals(order.getOrderStatus())) {
            return error("订单状态不正确");
        }
        // 返回任务信息和支付参数
        MallPayment mallPayment=mallPaymentService.createMallPayOrder(order,payType);
        Map<String, Object> responseData = new java.util.HashMap<>();
        responseData.put("paymentTitle", "商城购物订单");
        responseData.put("paymentAmount", mallPayment.getPayAmount());
        responseData.put("paymentOrderNo", mallPayment.getOrderNo());
        responseData.put("paymentString", mallPayment.getPayOrderString());
        responseData.put("payExpirationTime", DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS,DateUtils.addDays(mallPayment.getCreateTime(),1)));
        return AjaxResult.success(responseData);
    }
    /**
     * 获取用户订单统计
     */
    @ApiOperation("获取用户订单统计")
    @GetMapping("/stats")
    public AjaxResult getUserOrderStats()
    {
        Long userId = getUserId();
        Object stats = mallOrderService.getUserOrderStats(userId);
        return success(stats);
    }
}
