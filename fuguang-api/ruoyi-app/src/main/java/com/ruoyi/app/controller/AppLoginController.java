package com.ruoyi.app.controller;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.app.service.AppLoginService;
import com.ruoyi.fuguang.domain.AppUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * APP登录验证控制器
 * 提供用户登录、注册、短信验证码等功能
 *
 * <AUTHOR>
 */
@Api(tags = "APP登录接口", description = "APP用户登录、注册、短信验证相关接口")
@RestController("appLoginApiController")
@RequestMapping("/app")
public class AppLoginController
{
    @Autowired
    private AppLoginService appLoginService;

    /**
     * APP用户登录
     * 支持用户名密码登录和手机号验证码登录两种方式
     *
     * @param loginBody 登录信息，包含用户名/手机号、密码/验证码、登录类型等
     * @return 登录结果，包含token和用户信息
     */
    @Anonymous
    @ApiOperation(value = "APP用户登录",
                  notes = "支持两种登录方式：\n" +
                         "1. 用户名密码登录：loginType不传或传空，username和password必填\n" +
                         "2. 手机验证码登录：loginType传'sms'，phonenumber和smsCode必填")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "loginBody", value = "登录信息", required = true, dataType = "LoginBody", paramType = "body")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "登录成功，返回token和用户信息"),
        @ApiResponse(code = 500, message = "登录失败，用户名密码错误或验证码错误")
    })
    @PostMapping("/login")
    public AjaxResult login(@ApiParam(value = "登录信息", required = true) @RequestBody LoginBody loginBody)
    {
        String token;
        AppUser user;

        // 根据登录类型选择不同的登录方式
        if ("sms".equals(loginBody.getLoginType()))
        {
            // 手机验证码登录
            token = appLoginService.smsLogin(loginBody.getUsername(), loginBody.getSmsCode());
            user = appLoginService.getUserInfo(loginBody.getUsername());
        }
        else
        {
            // 密码登录
            token = appLoginService.login(loginBody.getUsername(), loginBody.getPassword());
            user = appLoginService.getUserInfo(loginBody.getUsername());
        }

        if (user != null) {
            user.setPassword(null); // 不返回密码
        }

        AjaxResult ajax = AjaxResult.success();
        ajax.put("token", token);
        ajax.put("user", user);
        return ajax;
    }

    /**
     * APP用户注册
     * 用户名密码注册，注册成功后可直接登录
     *
     * @param loginBody 注册信息，包含用户名和密码
     * @return 注册结果
     */
    @Anonymous
    @ApiOperation(value = "APP用户注册",
                  notes = "用户名密码注册，用户名需唯一，密码长度6-20位")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "loginBody", value = "注册信息", required = true, dataType = "LoginBody", paramType = "body")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "注册成功"),
        @ApiResponse(code = 500, message = "注册失败，用户名已存在或参数错误")
    })
    @PostMapping("/register")
    public AjaxResult register(@ApiParam(value = "注册信息，username和password必填", required = true) @RequestBody LoginBody loginBody)
    {
        String msg = appLoginService.register(loginBody.getUsername(), loginBody.getPassword());
        return AjaxResult.success(msg);
    }

    /**
     * 发送短信验证码
     * 用于手机号登录或注册时的验证码发送
     *
     * @param loginBody 包含手机号的请求体
     * @return 发送结果
     */
    @Anonymous
    @ApiOperation(value = "发送短信验证码",
                  notes = "向指定手机号发送6位数字验证码，有效期5分钟，同一手机号1分钟内只能发送一次")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "loginBody", value = "手机号信息", required = true, dataType = "LoginBody", paramType = "body")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "验证码发送成功"),
        @ApiResponse(code = 500, message = "发送失败，手机号格式错误或发送频率过快")
    })
    @PostMapping("/sendSmsCode")
    public AjaxResult sendSmsCode(@ApiParam(value = "手机号信息，phonenumber必填", required = true) @RequestBody LoginBody loginBody)
    {
        String msg = appLoginService.sendSmsCode(loginBody.getUsername());
        return AjaxResult.success(msg);
    }

    /**
     * 生成登录标识
     * 生成一个系统唯一标识，存储在Redis中，有效期为3分钟
     *
     * @return 登录标识
     */
    @Anonymous
    @ApiOperation(value = "生成登录标识",
                  notes = "生成一个系统唯一标识，用于后续登录验证，有效期3分钟")
    @ApiResponses({
        @ApiResponse(code = 200, message = "标识生成成功"),
        @ApiResponse(code = 500, message = "标识生成失败")
    })
    @PostMapping("/generateLoginIdentifier")
    public AjaxResult generateLoginIdentifier()
    {
        String identifier = appLoginService.generateLoginIdentifier();
        return AjaxResult.success("登录标识生成成功", identifier);
    }

    /**
     * 标识登录
     * 使用手机号和登录标识进行登录，标识验证通过后执行与验证码登录一致的逻辑
     *
     * @param loginBody 登录信息，包含手机号和登录标识
     * @return 登录结果，包含token和用户信息
     */
    @Anonymous
    @ApiOperation(value = "标识登录",
                  notes = "使用手机号和登录标识进行登录，标识验证通过后执行与验证码登录一致的逻辑")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "loginBody", value = "登录信息", required = true, dataType = "LoginBody", paramType = "body")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "登录成功"),
        @ApiResponse(code = 500, message = "登录失败，标识无效或用户不存在")
    })
    @PostMapping("/identifierLogin")
    public AjaxResult identifierLogin(@ApiParam(value = "登录信息，username(手机号)和loginIdentifier必填", required = true) @RequestBody LoginBody loginBody)
    {
        String token = appLoginService.identifierLogin(loginBody.getUsername(), loginBody.getLoginIdentifier());
        AppUser user = appLoginService.getUserInfo(loginBody.getUsername());

        if (user != null) {
            user.setPassword(null); // 不返回密码
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put("token", token);
        ajax.put("user", user);
        return ajax;
    }


}
