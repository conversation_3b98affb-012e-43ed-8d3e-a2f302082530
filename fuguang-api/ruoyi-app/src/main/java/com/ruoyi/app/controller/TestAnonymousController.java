package com.ruoyi.app.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;

/**
 * 测试Anonymous注解控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test")
public class TestAnonymousController
{
    /**
     * 测试Anonymous注解的接口
     */
    @Anonymous
    @GetMapping("/anonymous")
    public AjaxResult testAnonymous()
    {
        return AjaxResult.success("Anonymous注解生效，可以匿名访问");
    }
    
    /**
     * 测试需要认证的接口
     */
    @GetMapping("/auth")
    public AjaxResult testAuth()
    {
        return AjaxResult.success("需要认证才能访问");
    }
}
