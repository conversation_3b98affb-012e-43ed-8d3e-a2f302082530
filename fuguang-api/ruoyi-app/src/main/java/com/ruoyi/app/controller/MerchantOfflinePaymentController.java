package com.ruoyi.app.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fuguang.domain.MerchantApplication;
import com.ruoyi.fuguang.domain.OfflinePayment;
import com.ruoyi.fuguang.service.IMerchantApplicationService;
import com.ruoyi.fuguang.service.IOfflinePaymentService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static com.ruoyi.common.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * APP商家线下支付管理控制器
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Api(tags = "APP商家线下支付", description = "APP商家线下支付相关接口")
@RestController("MerchantOfflinePaymentController")
@RequestMapping("/app/merchant/offlinePayment")
public class MerchantOfflinePaymentController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(MerchantOfflinePaymentController.class);

    @Autowired
    private IOfflinePaymentService offlinePaymentService;

    /**
     * 生成商家二维码
     */
    @ApiOperation("生成商家二维码")
    @GetMapping("/qrcode")
    public AjaxResult generateMerchantQrcode(@ApiParam("让利比例") @RequestParam BigDecimal offerDiscounts)
    {
        return offlinePaymentService.generateMerchantQrcode(getUserId(),offerDiscounts);
    }

    /**
     * 获取商家二维码
     */
    @ApiOperation("获取商家二维码")
    @GetMapping("/qrcode/info")
    public AjaxResult getMerchantQrcode()
    {
        return success(offlinePaymentService.getMerchantQrcode(getUserId()));
    }

    /**
     * 创建线下支付订单
     */
    @ApiOperation("创建线下支付订单")
    @PostMapping("/create")
    public AjaxResult createOfflinePayOrder(@RequestBody Map<String, Object> params)
    {
        String qrcodeId = (String) params.get("qrcodeId");
        BigDecimal payAmount = BigDecimal.valueOf((Double) params.get("payAmount"));
        String payType = StringUtils.isNull(params.get("payType"))?"1":(String) params.get("payType");

        if ( StringUtils.isEmpty(qrcodeId) || StringUtils.isNull(payAmount) || payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return error("参数错误");
        }
        OfflinePayment payData=offlinePaymentService.createOfflinePayOrder(qrcodeId, payAmount,payType);
        // 返回任务信息和支付参数
        Map<String, Object> responseData = new java.util.HashMap<>();
        responseData.put("maintenanceFee", payData.getPlatformFee());//手续费
        responseData.put("guarantee", payData.getAmountDiscounts());//保障金
        responseData.put("paymentTitle", payData.getMerchantName());
        responseData.put("paymentAmount", payData.getPayAmount());
        responseData.put("paymentOrderNo", payData.getOrderNo());
        responseData.put("paymentString", payData.getPayOrderString());
        responseData.put("payExpirationTime", DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS,DateUtils.addDays(payData.getCreateTime(),1)));
        return success(responseData);
    }
}
