package com.ruoyi.web.controller.fuguang;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppUserProfile;
import com.ruoyi.fuguang.service.IAppUserProfileService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP用户个人简介Controller
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@RestController
@RequestMapping("/fuguang/profile")
public class AppUserProfileController extends BaseController
{
    @Autowired
    private IAppUserProfileService appUserProfileService;

    /**
     * 查询APP用户个人简介列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:profile:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppUserProfile appUserProfile)
    {
        startPage();
        List<AppUserProfile> list = appUserProfileService.selectAppUserProfileList(appUserProfile);
        return getDataTable(list);
    }

    /**
     * 导出APP用户个人简介列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:profile:export')")
    @Log(title = "APP用户个人简介", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppUserProfile appUserProfile)
    {
        List<AppUserProfile> list = appUserProfileService.selectAppUserProfileList(appUserProfile);
        ExcelUtil<AppUserProfile> util = new ExcelUtil<AppUserProfile>(AppUserProfile.class);
        util.exportExcel(response, list, "APP用户个人简介数据");
    }

    /**
     * 获取APP用户个人简介详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:profile:query')")
    @GetMapping(value = "/{profileId}")
    public AjaxResult getInfo(@PathVariable("profileId") Long profileId)
    {
        return success(appUserProfileService.selectAppUserProfileByProfileId(profileId));
    }

    /**
     * 根据用户ID获取APP用户个人简介详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:profile:query')")
    @GetMapping(value = "/user/{userId}")
    public AjaxResult getInfoByUserId(@PathVariable("userId") Long userId)
    {
        return success(appUserProfileService.selectAppUserProfileByUserId(userId));
    }

    /**
     * 新增APP用户个人简介
     */
    @PreAuthorize("@ss.hasPermi('fuguang:profile:add')")
    @Log(title = "APP用户个人简介", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppUserProfile appUserProfile)
    {
        return toAjax(appUserProfileService.insertAppUserProfile(appUserProfile));
    }

    /**
     * 修改APP用户个人简介
     */
    @PreAuthorize("@ss.hasPermi('fuguang:profile:edit')")
    @Log(title = "APP用户个人简介", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppUserProfile appUserProfile)
    {
        return toAjax(appUserProfileService.updateAppUserProfile(appUserProfile));
    }

    /**
     * 删除APP用户个人简介
     */
    @PreAuthorize("@ss.hasPermi('fuguang:profile:remove')")
    @Log(title = "APP用户个人简介", businessType = BusinessType.DELETE)
	@DeleteMapping("/{profileIds}")
    public AjaxResult remove(@PathVariable Long[] profileIds)
    {
        return toAjax(appUserProfileService.deleteAppUserProfileByProfileIds(profileIds));
    }

    /**
     * 更新用户信用分
     */
    @PreAuthorize("@ss.hasPermi('fuguang:profile:edit')")
    @Log(title = "更新用户信用分", businessType = BusinessType.UPDATE)
    @PutMapping("/creditScore/{userId}/{score}")
    public AjaxResult updateCreditScore(@PathVariable Long userId, @PathVariable Integer score)
    {
        boolean result = appUserProfileService.updateCreditScore(userId, score);
        return result ? success("信用分更新成功") : error("信用分更新失败");
    }

    /**
     * 更新用户任务分
     */
    @PreAuthorize("@ss.hasPermi('fuguang:profile:edit')")
    @Log(title = "更新用户任务分", businessType = BusinessType.UPDATE)
    @PutMapping("/taskScore/{userId}/{score}")
    public AjaxResult updateTaskScore(@PathVariable Long userId, @PathVariable Integer score)
    {
        boolean result = appUserProfileService.updateTaskScore(userId, score);
        return result ? success("任务分更新成功") : error("任务分更新失败");
    }

    /**
     * 设置扶贫救援徽章
     */
    @PreAuthorize("@ss.hasPermi('fuguang:profile:edit')")
    @Log(title = "设置扶贫救援徽章", businessType = BusinessType.UPDATE)
    @PutMapping("/povertyBadge/{userId}/{badge}")
    public AjaxResult updatePovertyReliefBadge(@PathVariable Long userId, @PathVariable String badge)
    {
        boolean result = appUserProfileService.updatePovertyReliefBadge(userId, badge);
        return result ? success("徽章设置成功") : error("徽章设置失败");
    }

    /**
     * 初始化用户个人简介
     */
    @PreAuthorize("@ss.hasPermi('fuguang:profile:add')")
    @Log(title = "初始化用户个人简介", businessType = BusinessType.INSERT)
    @PostMapping("/init/{userId}")
    public AjaxResult initUserProfile(@PathVariable Long userId)
    {
        appUserProfileService.initUserProfile(userId);
        return success("初始化成功");
    }
}
